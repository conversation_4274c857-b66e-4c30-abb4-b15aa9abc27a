// src/components/trips/TripComparisonModal.tsx
/**
 * Modale principale per il confronto side-by-side dei viaggi
 * Visualizza fino a 3 viaggi in colonne verticali con tutte le informazioni richieste
 */

'use client'

import { useEffect } from 'react'
import { X } from 'lucide-react'
import { TripComparisonModalProps } from '@/types/comparison'
import { TripComparisonCard } from './TripComparisonCard'

export function TripComparisonModal({
  isOpen,
  onClose,
  trips,
  onRemoveTrip
}: TripComparisonModalProps) {
  // Gestisce la chiusura con ESC
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Previene lo scroll del body quando la modale è aperta
      document.body.style.overflow = 'hidden'
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])
  
  // Non renderizzare se non è aperta
  if (!isOpen) return null
  
  // Gestisce il click sul backdrop
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }
  
  return (
    <div 
      className="fixed inset-0 z-50 overflow-y-auto"
      onClick={handleBackdropClick}
    >
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" />
      
      {/* Container modale */}
      <div className="relative min-h-screen flex items-center justify-center p-4">
        <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden">
          
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                Confronto Viaggi
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {trips.length === 1 
                  ? '1 viaggio selezionato'
                  : `${trips.length} viaggi selezionati`
                }
              </p>
            </div>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Chiudi confronto"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          
          {/* Contenuto principale */}
          <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
            {trips.length === 0 ? (
              // Stato vuoto
              <div className="flex flex-col items-center justify-center py-16 px-6">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <X className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nessun viaggio selezionato
                </h3>
                <p className="text-gray-600 text-center max-w-md">
                  Seleziona almeno 2 viaggi dalla lista per confrontarli qui.
                </p>
              </div>
            ) : (
              // Griglia di confronto
              <div className={`
                grid gap-6 p-6
                ${trips.length === 1 ? 'grid-cols-1 max-w-md mx-auto' : ''}
                ${trips.length === 2 ? 'grid-cols-1 md:grid-cols-2' : ''}
                ${trips.length === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : ''}
              `}>
                {trips.map((trip, index) => (
                  <TripComparisonCard
                    key={trip.id}
                    trip={trip}
                    onRemove={onRemoveTrip}
                    className={`
                      ${trips.length === 1 ? 'w-full' : ''}
                      ${trips.length === 2 ? 'w-full' : ''}
                      ${trips.length === 3 ? 'w-full' : ''}
                    `}
                  />
                ))}
              </div>
            )}
          </div>
          
          {/* Footer con azioni */}
          {trips.length > 0 && (
            <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  {trips.length < 3 && (
                    <span>
                      Puoi aggiungere ancora {3 - trips.length} viaggio{3 - trips.length !== 1 ? 'i' : ''} al confronto
                    </span>
                  )}
                  {trips.length === 3 && (
                    <span>Limite massimo raggiunto (3 viaggi)</span>
                  )}
                </div>
                
                <div className="flex gap-3">
                  <button
                    onClick={() => {
                      trips.forEach(trip => onRemoveTrip(trip.id))
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancella Tutto
                  </button>
                  
                  <button
                    onClick={onClose}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Chiudi
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

/**
 * Hook per gestire la modale di confronto
 * Fornisce funzioni di utilità per aprire/chiudere la modale
 */
export function useComparisonModal() {
  const handleOpen = () => {
    // Logica aggiuntiva per l'apertura se necessaria
  }
  
  const handleClose = () => {
    // Logica aggiuntiva per la chiusura se necessaria
  }
  
  return {
    handleOpen,
    handleClose
  }
}

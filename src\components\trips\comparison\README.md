# Sistema di Confronto Viaggi

Il sistema di confronto viaggi permette agli utenti Explorer/Ranger di confrontare fino a 3 viaggi side-by-side in una vista modale comparativa.

## 🚀 Funzionalità Principali

- **Selezione Viaggi**: Aggiungi fino a 3 viaggi al confronto
- **Vista Comparativa**: Modale responsive con layout a colonne
- **Calcolo <PERSON>**: Distanza totale calcolata automaticamente dalle tappe
- **Indicatore Floating**: Badge che mostra il numero di viaggi selezionati
- **Persistenza**: Selezione salvata in localStorage per la sessione

## 📊 Informazioni Visualizzate

Per ogni viaggio nel confronto vengono mostrate:

- **<PERSON><PERSON>** del viaggio
- **Immagine principale** (o placeholder se non disponibile)
- **Destinazione** con icona MapPin
- **Durata** in giorni con icona Clock
- **Distanza totale** calcolata con icona Navigation
- **Stagioni consigliate** con icone specifiche
- **Prezzo** formattato in euro
- **Link** ai dettagli completi del viaggio

## 🏗️ Architettura

### Componenti Principali

```typescript
// Hook di gestione stato
useTripComparison()

// Componenti UI
<ComparisonButton />
<ComparisonIndicator />
<TripComparisonModal />
<TripComparisonCard />

// Provider per contesto globale
<TripComparisonProvider />
```

### Tipi TypeScript

```typescript
interface TripComparisonData {
  id: string
  title: string
  destination: string
  duration_days: number
  totalDistance: number
  recommended_seasons: RecommendedSeason[]
  price: number
  coverImage?: MediaItem
  slug: string
}
```

## 🎯 Utilizzo

### Setup Base

```tsx
import { TripComparisonProvider } from '@/components/trips/comparison'

function App() {
  return (
    <TripComparisonProvider>
      {/* La tua app */}
    </TripComparisonProvider>
  )
}
```

### Aggiungere Pulsanti di Confronto

```tsx
import { ComparisonButton, convertTripToComparisonData } from '@/components/trips/comparison'

function TripCard({ trip }) {
  const comparisonData = convertTripToComparisonData(trip)
  
  return (
    <div className="trip-card">
      {/* Contenuto della card */}
      <ComparisonButton trip={comparisonData} />
    </div>
  )
}
```

### Hook per Gestione Stato

```tsx
import { useTripComparison } from '@/components/trips/comparison'

function MyComponent() {
  const {
    selectedTrips,
    selectedCount,
    canCompare,
    addTrip,
    removeTrip,
    openModal,
    closeModal
  } = useTripComparison()
  
  // Usa le funzioni per gestire il confronto
}
```

## 🎨 Varianti Componenti

### ComparisonButton

```tsx
// Variante default
<ComparisonButton trip={tripData} />

// Variante compatta
<ComparisonButton trip={tripData} variant="compact" />

// Con badge di stato
<ComparisonButtonWithBadge trip={tripData} />
```

### ComparisonIndicator

```tsx
// Indicatore completo
<ComparisonIndicator 
  selectedCount={count}
  onOpenComparison={openModal}
  onClearAll={clearAll}
/>

// Versione compatta
<CompactComparisonIndicator {...props} />

// Versione minimale
<MinimalComparisonIndicator {...props} />
```

## 🔧 Configurazione

### Costanti

```typescript
export const COMPARISON_CONFIG = {
  MAX_TRIPS: 3,
  MIN_TRIPS_FOR_COMPARISON: 2,
  STORAGE_KEY: 'rideatlas_trip_comparison',
  SESSION_TIMEOUT: 1000 * 60 * 60 * 2, // 2 ore
}
```

### Messaggi

```typescript
export const COMPARISON_MESSAGES = {
  MAX_TRIPS_REACHED: 'Puoi confrontare al massimo 3 viaggi',
  TRIP_ALREADY_SELECTED: 'Questo viaggio è già stato selezionato',
  MIN_TRIPS_REQUIRED: 'Seleziona almeno 2 viaggi per confrontarli',
  // ...altri messaggi
}
```

## 📱 Design Responsive

Il sistema è ottimizzato per tutti i dispositivi:

- **Desktop**: Layout a 3 colonne
- **Tablet**: Layout a 2 colonne con scroll
- **Mobile**: Layout a 1 colonna con stack verticale

## 🧪 Testing

```bash
# Esegui i test del sistema di confronto
npm test -- TripComparison.test.tsx

# Test di integrazione
npm run test:integration
```

## 🔄 Flusso Utente

1. **Selezione**: L'utente clicca "Confronta" su una card viaggio
2. **Indicazione**: Appare l'indicatore floating con il conteggio
3. **Aggiunta**: L'utente può selezionare fino a 3 viaggi totali
4. **Confronto**: Click su "Confronta" apre la modale side-by-side
5. **Navigazione**: Dall'interno della modale può andare ai dettagli

## 🛠️ Integrazione con Sistema Esistente

- **Calcolo Distanze**: Utilizza `calculateTotalDistance` da `trip-utils.ts`
- **Componenti UI**: Integrato con SeasonIcon, toast system, etc.
- **Routing**: Compatible con Next.js App Router
- **Styling**: Utilizza TailwindCSS esistente

## 📈 Performance

- **Lazy Loading**: Componenti caricati solo quando necessari
- **Memoization**: Hook ottimizzati per evitare re-render
- **Storage**: Persistenza efficiente con cleanup automatico
- **Bundle Size**: Componenti tree-shakeable

## 🔒 Sicurezza

- **Validazione**: Input sanitizzati e validati
- **XSS Protection**: Escape di contenuti utente
- **Storage**: Dati sensibili non salvati in localStorage

## 🚀 Estensioni Future

- Filtri e ordinamento nel confronto
- Esportazione confronto in PDF
- Condivisione confronto via URL
- Confronto avanzato con metriche aggiuntive

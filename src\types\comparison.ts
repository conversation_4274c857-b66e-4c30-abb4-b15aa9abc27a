// src/types/comparison.ts
/**
 * Tipi TypeScript per la funzionalità di confronto viaggi
 */

import { RecommendedSeason } from '@prisma/client'
import { MediaItem } from './trip'

/**
 * Dati essenziali di un viaggio per il confronto
 * Include solo le informazioni necessarie per la vista comparativa
 */
export interface TripComparisonData {
  id: string
  title: string
  destination: string
  duration_days: number
  totalDistance: number // Calcolata con calculateTotalDistance
  recommended_seasons: RecommendedSeason[]
  price: number
  coverImage?: MediaItem
  slug: string
}

/**
 * Stato del sistema di confronto viaggi
 */
export interface ComparisonState {
  selectedTrips: TripComparisonData[]
  isModalOpen: boolean
  maxTrips: number
}

/**
 * Interfaccia per l'hook di gestione del confronto
 */
export interface ComparisonHook {
  // Stato
  selectedTrips: TripComparisonData[]
  isModalOpen: boolean
  canAddMore: boolean
  selectedCount: number
  
  // Azioni
  addTrip: (trip: TripComparisonData) => boolean
  removeTrip: (tripId: string) => void
  clearAll: () => void
  openModal: () => void
  closeModal: () => void
  isSelected: (tripId: string) => boolean
  
  // Utilità
  canCompare: boolean // true se ci sono almeno 2 viaggi
}

/**
 * Props per il componente ComparisonButton
 */
export interface ComparisonButtonProps {
  trip: TripComparisonData
  className?: string
  variant?: 'default' | 'compact'
}

/**
 * Props per il componente TripComparisonModal
 */
export interface TripComparisonModalProps {
  isOpen: boolean
  onClose: () => void
  trips: TripComparisonData[]
  onRemoveTrip: (tripId: string) => void
}

/**
 * Props per il componente ComparisonIndicator
 */
export interface ComparisonIndicatorProps {
  selectedCount: number
  onOpenComparison: () => void
  onClearAll: () => void
  className?: string
}

/**
 * Props per il componente TripComparisonCard
 */
export interface TripComparisonCardProps {
  trip: TripComparisonData
  onRemove: (tripId: string) => void
  className?: string
}

/**
 * Configurazione per il sistema di confronto
 */
export const COMPARISON_CONFIG = {
  MAX_TRIPS: 3,
  MIN_TRIPS_FOR_COMPARISON: 2,
  STORAGE_KEY: 'rideatlas_trip_comparison',
  SESSION_TIMEOUT: 1000 * 60 * 60 * 2, // 2 ore
} as const

/**
 * Messaggi di errore per il sistema di confronto
 */
export const COMPARISON_MESSAGES = {
  MAX_TRIPS_REACHED: `Puoi confrontare al massimo ${COMPARISON_CONFIG.MAX_TRIPS} viaggi`,
  TRIP_ALREADY_SELECTED: 'Questo viaggio è già stato selezionato per il confronto',
  MIN_TRIPS_REQUIRED: `Seleziona almeno ${COMPARISON_CONFIG.MIN_TRIPS_FOR_COMPARISON} viaggi per confrontarli`,
  TRIP_ADDED: 'Viaggio aggiunto al confronto',
  TRIP_REMOVED: 'Viaggio rimosso dal confronto',
  ALL_CLEARED: 'Tutti i viaggi rimossi dal confronto',
} as const

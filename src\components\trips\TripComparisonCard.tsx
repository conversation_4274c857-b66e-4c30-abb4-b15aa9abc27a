// src/components/trips/TripComparisonCard.tsx
/**
 * Card singola per la visualizzazione di un viaggio nel confronto
 * Mostra tutte le informazioni richieste in formato compatto
 */

'use client'

import Link from 'next/link'
import { MapPin, Clock, Navigation, X, ExternalLink } from 'lucide-react'
import { TripComparisonCardProps } from '@/types/comparison'
import { SeasonIcon } from '@/components/ui/SeasonIcon'
import { formatDistance, formatPrice, formatDuration } from '@/lib/trips/comparison-utils'

export function TripComparisonCard({
  trip,
  onRemove,
  className = ''
}: TripComparisonCardProps) {
  
  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow ${className}`}>
      
      {/* Header con pulsante rimozione */}
      <div className="relative">
        {/* Immagine di copertina */}
        <div className="relative h-48 bg-gradient-to-br from-blue-400 to-purple-500">
          {trip.coverImage ? (
            <img
              src={trip.coverImage.url}
              alt={trip.coverImage.caption || trip.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-white text-center">
                <Navigation className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm opacity-75">Nessuna immagine</p>
              </div>
            </div>
          )}
          
          {/* Overlay gradient per migliorare la leggibilità */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        </div>
        
        {/* Pulsante rimozione */}
        <button
          onClick={() => onRemove(trip.id)}
          className="absolute top-3 right-3 p-1.5 bg-white/90 hover:bg-white text-gray-600 hover:text-gray-800 rounded-full shadow-sm transition-all"
          title="Rimuovi dal confronto"
          aria-label={`Rimuovi ${trip.title} dal confronto`}
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      
      {/* Contenuto principale */}
      <div className="p-5">
        
        {/* Titolo */}
        <h3 className="font-bold text-lg text-gray-900 mb-3 line-clamp-2">
          {trip.title}
        </h3>
        
        {/* Informazioni principali */}
        <div className="space-y-3 mb-4">
          
          {/* Destinazione */}
          <div className="flex items-center gap-2 text-gray-600">
            <MapPin className="w-4 h-4 flex-shrink-0" />
            <span className="text-sm font-medium">{trip.destination}</span>
          </div>
          
          {/* Durata */}
          <div className="flex items-center gap-2 text-gray-600">
            <Clock className="w-4 h-4 flex-shrink-0" />
            <span className="text-sm">{formatDuration(trip.duration_days)}</span>
          </div>
          
          {/* Distanza totale */}
          <div className="flex items-center gap-2 text-gray-600">
            <Navigation className="w-4 h-4 flex-shrink-0" />
            <span className="text-sm">{formatDistance(trip.totalDistance)}</span>
          </div>
        </div>
        
        {/* Stagioni consigliate */}
        {trip.recommended_seasons.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Stagioni consigliate
            </h4>
            <div className="flex flex-wrap gap-2">
              {trip.recommended_seasons.map((season) => (
                <div
                  key={season}
                  className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-full"
                >
                  <SeasonIcon season={season} className="w-3 h-3" />
                  <span className="text-xs font-medium text-gray-700">
                    {season === 'Primavera' && 'Primavera'}
                    {season === 'Estate' && 'Estate'}
                    {season === 'Autunno' && 'Autunno'}
                    {season === 'Inverno' && 'Inverno'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Prezzo */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Prezzo</span>
            <span className="text-lg font-bold text-blue-600">
              {formatPrice(trip.price)}
            </span>
          </div>
        </div>
        
        {/* Azioni */}
        <div className="flex gap-2 pt-3 border-t border-gray-100">
          <Link
            href={`/trips/${trip.slug}`}
            className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ExternalLink className="w-4 h-4" />
            Vedi Dettagli
          </Link>
          
          <button
            onClick={() => onRemove(trip.id)}
            className="px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors"
          >
            Rimuovi
          </button>
        </div>
      </div>
    </div>
  )
}

/**
 * Versione compatta della card per layout più stretti
 */
export function CompactTripComparisonCard({
  trip,
  onRemove,
  className = ''
}: TripComparisonCardProps) {
  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm ${className}`}>
      
      {/* Layout orizzontale compatto */}
      <div className="flex">
        
        {/* Immagine piccola */}
        <div className="relative w-24 h-24 flex-shrink-0">
          {trip.coverImage ? (
            <img
              src={trip.coverImage.url}
              alt={trip.coverImage.caption || trip.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
              <Navigation className="w-6 h-6 text-white opacity-50" />
            </div>
          )}
        </div>
        
        {/* Contenuto */}
        <div className="flex-1 p-3">
          <div className="flex items-start justify-between mb-2">
            <h4 className="font-medium text-sm text-gray-900 line-clamp-1">
              {trip.title}
            </h4>
            <button
              onClick={() => onRemove(trip.id)}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Rimuovi"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
          
          <div className="space-y-1 text-xs text-gray-600">
            <div className="flex items-center gap-1">
              <MapPin className="w-3 h-3" />
              <span>{trip.destination}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{formatDuration(trip.duration_days)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium text-blue-600">
                {formatPrice(trip.price)}
              </span>
              <Link
                href={`/trips/${trip.slug}`}
                className="text-blue-600 hover:text-blue-800 transition-colors"
              >
                <ExternalLink className="w-3 h-3" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

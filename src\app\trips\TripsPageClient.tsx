// src/app/trips/TripsPageClient.tsx
/**
 * Componente client-side per la pagina dei viaggi con funzionalità di confronto
 */

'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Tag, User, Clock, Navigation, Scale } from 'lucide-react'
import { SeasonIcon } from '@/components/ui/SeasonIcon'
import { TripComparisonProvider, useTripComparisonContext } from '@/components/trips/TripComparisonProvider'
import { ComparisonButtonWithBadge } from '@/components/trips/ComparisonButton'
import { convertTripToComparisonData } from '@/lib/trips/comparison-utils'
import { castToMediaItems, MediaItem } from '@/types/trip'
import { Prisma } from '@prisma/client'

// Usa i tipi generati automaticamente da Prisma per le query con include
type TripWithRelations = Prisma.TripGetPayload<{
  include: {
    user: {
      select: {
        name: true;
        email: true;
        image: true;
      };
    };
    stages: {
      select: {
        media: true;
        orderIndex: true;
      };
    };
  };
}>;

type TripWithProcessedData = TripWithRelations & {
  processedGpxFile: any;
  processedMedia: MediaItem[];
};

interface TripsPageClientProps {
  trips: TripWithProcessedData[]
  uniqueDestinations: number
}

// Utility functions (moved from server component)
function getCoverImage(trip: TripWithProcessedData): MediaItem | null {
  // Prima cerca nelle media del viaggio principale
  if (trip.processedMedia && trip.processedMedia.length > 0) {
    const image = trip.processedMedia.find(item => item.type === 'image')
    if (image) return image
    
    const videoWithThumbnail = trip.processedMedia.find(
      item => item.type === 'video' && item.thumbnailUrl
    )
    if (videoWithThumbnail) return videoWithThumbnail
  }
  
  // Poi cerca nelle media delle stages
  if (trip.stages && trip.stages.length > 0) {
    for (const stage of trip.stages) {
      const stageMedia = castToMediaItems(stage.media)
      const image = stageMedia.find(item => item.type === 'image')
      if (image) return image
      
      const videoWithThumbnail = stageMedia.find(
        item => item.type === 'video' && item.thumbnailUrl
      )
      if (videoWithThumbnail) return videoWithThumbnail
    }
  }
  
  return null
}

function getThemeColor(theme: string): string {
  const themeColors: Record<string, string> = {
    'Avventura': 'bg-orange-100 text-orange-800',
    'Relax': 'bg-green-100 text-green-800',
    'Cultura': 'bg-purple-100 text-purple-800',
    'Natura': 'bg-emerald-100 text-emerald-800',
    'Gastronomia': 'bg-red-100 text-red-800',
    'Sport': 'bg-blue-100 text-blue-800',
  }
  return themeColors[theme] || 'bg-gray-100 text-gray-800'
}

function getSeasonColor(season: string): string {
  const seasonColors: Record<string, string> = {
    'Primavera': 'bg-green-100 text-green-800',
    'Estate': 'bg-yellow-100 text-yellow-800',
    'Autunno': 'bg-orange-100 text-orange-800',
    'Inverno': 'bg-blue-100 text-blue-800',
  }
  return seasonColors[season] || 'bg-gray-100 text-gray-800'
}

function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date))
}

// Componente per il pulsante di confronto nell'header
function ComparisonHeaderButton() {
  const { selectedCount, canCompare, openModal } = useTripComparisonContext()

  return (
    <div className="mt-4">
      {selectedCount > 0 ? (
        <button
          onClick={openModal}
          disabled={!canCompare}
          className={`
            inline-flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all
            ${canCompare
              ? 'bg-white text-primary-600 hover:bg-gray-100 shadow-lg'
              : 'bg-white/50 text-primary-300 cursor-not-allowed'
            }
          `}
        >
          <Scale className="w-5 h-5" />
          <span>
            {selectedCount === 1
              ? `1 viaggio selezionato - Aggiungi almeno un altro`
              : `Confronta ${selectedCount} viaggi`
            }
          </span>
        </button>
      ) : (
        <div className="text-white/70 text-sm">
          Seleziona almeno 2 viaggi per confrontarli
        </div>
      )}
    </div>
  )
}

export function TripsPageClient({ trips, uniqueDestinations }: TripsPageClientProps) {
  return (
    <TripComparisonProvider>
      <main className="min-h-screen bg-gray-50">
        {/* Header della pagina */}
        <section className="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-display font-bold mb-4">
                Tutti i Viaggi
              </h1>
              <p className="text-xl text-primary-100 max-w-2xl mx-auto">
                Scopri tutti gli itinerari creati dalla nostra community di motociclisti appassionati
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center mt-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg px-6 py-3">
                  <div className="text-2xl font-bold">{trips.length}</div>
                  <div className="text-sm text-primary-100">Itinerari Disponibili</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg px-6 py-3">
                  <div className="text-2xl font-bold">{uniqueDestinations}</div>
                  <div className="text-sm text-primary-100">Destinazioni Uniche</div>
                </div>
              </div>

              {/* Pulsante confronto nell'header */}
              <ComparisonHeaderButton />
            </div>
          </div>
        </section>

        {/* Contenuto principale */}
        <section className="container mx-auto px-4 py-12">

          {/* Istruzioni per il confronto */}
          <div className="mb-8 text-center">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-2xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Scale className="w-5 h-5 text-blue-600" />
                <h3 className="font-medium text-blue-900">Confronta i Viaggi</h3>
              </div>
              <p className="text-sm text-blue-700">
                Clicca sui pulsanti "Confronta" per selezionare fino a 3 viaggi e confrontarli side-by-side
              </p>
            </div>
          </div>

          {trips.length === 0 ? (
            // Stato vuoto
            <div className="text-center py-20">
              <div className="max-w-md mx-auto">
                <div className="mb-8">
                  <Navigation className="w-24 h-24 text-gray-300 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-gray-600 mb-2">
                    Nessun viaggio disponibile
                  </h2>
                  <p className="text-gray-500">
                    Non ci sono ancora viaggi pubblicati. Sii il primo a creare un itinerario!
                  </p>
                </div>
                <Link 
                  href="/create-trip" 
                  className="btn-primary inline-flex items-center gap-2"
                >
                  <Navigation className="w-5 h-5" />
                  Crea il primo itinerario
                </Link>
              </div>
            </div>
          ) : (
            // Griglia dei viaggi
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {trips.map((trip) => {
                const coverImage = getCoverImage(trip)
                const comparisonData = convertTripToComparisonData(trip)
                
                return (
                  <div 
                    key={trip.id} 
                    className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden"
                  >
                    {/* Media preview or placeholder */}
                    <div className="relative h-48 bg-gradient-to-br from-primary-400 to-secondary-500">
                      {coverImage ? (
                        <img 
                          src={coverImage.url} 
                          alt={coverImage.caption || trip.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <>
                          <div className="absolute inset-0 bg-black/20"></div>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <Navigation className="w-16 h-16 text-white/70" />
                          </div>
                        </>
                      )}
                      
                      {/* Pulsante confronto */}
                      <div className="absolute top-4 left-4">
                        <ComparisonButtonWithBadge 
                          trip={comparisonData}
                          variant="compact"
                        />
                      </div>
                    </div>

                    {/* Contenuto della card */}
                    <div className="p-6">
                      {/* Titolo */}
                      <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                        {trip.title}
                      </h3>

                      {/* Destinazione */}
                      <div className="flex items-center gap-2 text-gray-600 mb-3">
                        <MapPin className="w-4 h-4" />
                        <span className="font-medium">{trip.destination}</span>
                      </div>

                      {/* Durata */}
                      <div className="flex items-center gap-2 text-gray-600 mb-4">
                        <Clock className="w-4 h-4" />
                        <span>{trip.duration_days} giorni</span>
                      </div>

                      {/* Tema */}
                      <div className="mb-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getThemeColor(trip.theme)}`}>
                          {trip.theme}
                        </span>
                      </div>

                      {/* Stagioni consigliate */}
                      {trip.recommended_seasons.length > 0 && (
                        <div className="mb-4">
                          <div className="flex flex-wrap gap-2">
                            {trip.recommended_seasons.slice(0, 3).map((season, index) => (
                              <span 
                                key={index} 
                                className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getSeasonColor(season)}`}
                              >
                                <SeasonIcon season={season} size="w-3 h-3" />
                                {season}
                              </span>
                            ))}
                            {trip.recommended_seasons.length > 3 && (
                              <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                                +{trip.recommended_seasons.length - 3}
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Creatore del viaggio */}
                      <div className="border-t pt-4 mt-4">
                        <div className="flex items-center gap-3 mb-2">
                          {trip.user.image ? (
                            <Image
                              src={trip.user.image}
                              alt={trip.user.name || 'Utente'}
                              width={32}
                              height={32}
                              className="rounded-full"
                            />
                          ) : (
                            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                              <User className="w-4 h-4 text-gray-600" />
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {trip.user.name || 'Utente anonimo'}
                            </p>
                          </div>
                        </div>
                        
                        {/* Data del viaggio - solo se disponibile */}
                        {trip.travelDate && (
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Calendar className="w-3 h-3" />
                            <span>Viaggio del {formatDate(trip.travelDate)}</span>
                          </div>
                        )}
                      </div>

                      {/* Azioni */}
                      <div className="mt-6 pt-4 border-t flex gap-2">
                        <Link 
                          href={`/trips/${trip.slug}`}
                          className="flex-1 btn-primary text-center"
                        >
                          Visualizza Dettagli
                        </Link>
                        
                        <ComparisonButtonWithBadge 
                          trip={comparisonData}
                          variant="default"
                          className="flex-shrink-0"
                        />
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}

          {/* Call to action per creare un nuovo viaggio */}
          {trips.length > 0 && (
            <div className="mt-16 text-center">
              <div className="bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Hai un itinerario da condividere?
                </h2>
                <p className="text-gray-600 mb-6">
                  Unisciti alla nostra community e condividi i tuoi percorsi preferiti con altri motociclisti
                </p>
                <Link 
                  href="/create-trip" 
                  className="btn-primary inline-flex items-center gap-2"
                >
                  <Navigation className="w-5 h-5" />
                  Crea il tuo itinerario
                </Link>
              </div>
            </div>
          )}
        </section>
      </main>
    </TripComparisonProvider>
  )
}

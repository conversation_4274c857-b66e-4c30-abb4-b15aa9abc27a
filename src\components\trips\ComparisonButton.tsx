// src/components/trips/ComparisonButton.tsx
/**
 * Componente pulsante per aggiungere/rimuovere viaggi dal confronto
 * Mostra stato visivo diverso se il viaggio è già selezionato
 */

'use client'

import { useState } from 'react'
import { Scale, Check, Plus } from 'lucide-react'
import { ComparisonButtonProps } from '@/types/comparison'
import { useTripComparison } from '@/hooks/trips/useTripComparison'

export function ComparisonButton({ 
  trip, 
  className = '', 
  variant = 'default' 
}: ComparisonButtonProps) {
  const { addTrip, removeTrip, isSelected, canAddMore } = useTripComparison()
  const [isLoading, setIsLoading] = useState(false)
  
  const isSelectedTrip = isSelected(trip.id)
  
  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault() // Previene la navigazione se il pulsante è dentro un Link
    e.stopPropagation() // Previene la propagazione dell'evento
    
    setIsLoading(true)
    
    try {
      if (isSelectedTrip) {
        removeTrip(trip.id)
      } else {
        addTrip(trip)
      }
    } finally {
      setIsLoading(false)
    }
  }
  
  // Determina se il pulsante deve essere disabilitato
  const isDisabled = isLoading || (!isSelectedTrip && !canAddMore)
  
  // Stili base comuni
  const baseClasses = `
    inline-flex items-center justify-center gap-2 
    font-medium rounded-lg transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
  `
  
  // Stili per variante default
  const defaultClasses = `
    px-3 py-2 text-sm
    ${isSelectedTrip 
      ? 'bg-green-100 text-green-800 border border-green-300 hover:bg-green-200 focus:ring-green-500' 
      : 'bg-blue-100 text-blue-800 border border-blue-300 hover:bg-blue-200 focus:ring-blue-500'
    }
  `
  
  // Stili per variante compatta
  const compactClasses = `
    p-2 text-xs
    ${isSelectedTrip 
      ? 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-500' 
      : 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500'
    }
  `
  
  const buttonClasses = `
    ${baseClasses}
    ${variant === 'compact' ? compactClasses : defaultClasses}
    ${className}
  `
  
  // Contenuto del pulsante basato sullo stato
  const getButtonContent = () => {
    if (isLoading) {
      return (
        <>
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          {variant === 'default' && <span>Caricamento...</span>}
        </>
      )
    }
    
    if (isSelectedTrip) {
      return (
        <>
          <Check className="w-4 h-4" />
          {variant === 'default' && <span>Selezionato</span>}
        </>
      )
    }
    
    return (
      <>
        {variant === 'compact' ? (
          <Scale className="w-4 h-4" />
        ) : (
          <>
            <Plus className="w-4 h-4" />
            <span>Confronta</span>
          </>
        )}
      </>
    )
  }
  
  // Tooltip per la variante compatta
  const getTooltip = () => {
    if (variant !== 'compact') return undefined
    
    if (isSelectedTrip) return 'Rimuovi dal confronto'
    if (!canAddMore) return 'Limite massimo raggiunto (3 viaggi)'
    return 'Aggiungi al confronto'
  }
  
  return (
    <button
      onClick={handleClick}
      disabled={isDisabled}
      className={buttonClasses}
      title={getTooltip()}
      aria-label={
        isSelectedTrip 
          ? `Rimuovi ${trip.title} dal confronto`
          : `Aggiungi ${trip.title} al confronto`
      }
    >
      {getButtonContent()}
    </button>
  )
}

/**
 * Variante compatta del pulsante per spazi ridotti
 */
export function CompactComparisonButton({ 
  trip, 
  className = '' 
}: Omit<ComparisonButtonProps, 'variant'>) {
  return (
    <ComparisonButton 
      trip={trip} 
      variant="compact" 
      className={className} 
    />
  )
}

/**
 * Wrapper per il pulsante con badge di stato
 * Mostra un piccolo indicatore quando il viaggio è selezionato
 */
export function ComparisonButtonWithBadge({ 
  trip, 
  className = '',
  variant = 'default'
}: ComparisonButtonProps) {
  const { isSelected } = useTripComparison()
  const isSelectedTrip = isSelected(trip.id)
  
  return (
    <div className={`relative ${className}`}>
      <ComparisonButton trip={trip} variant={variant} />
      
      {isSelectedTrip && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white">
          <span className="sr-only">Selezionato per il confronto</span>
        </div>
      )}
    </div>
  )
}

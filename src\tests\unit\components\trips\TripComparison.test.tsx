// src/tests/unit/components/trips/TripComparison.test.tsx
/**
 * Test per il sistema di confronto viaggi
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useTripComparison } from '@/hooks/trips/useTripComparison'
import { ComparisonButton } from '@/components/trips/ComparisonButton'
import { TripComparisonModal } from '@/components/trips/TripComparisonModal'
import { convertTripToComparisonData, formatDistance, formatPrice } from '@/lib/trips/comparison-utils'
import { TripComparisonData } from '@/types/comparison'

// Mock del hook useToast
jest.mock('@/hooks/ui/useToast', () => ({
  useToast: () => ({
    showSuccess: jest.fn(),
    showError: jest.fn(),
  }),
}))

// Mock di localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Dati di test
const mockTrip: TripComparisonData = {
  id: 'trip-1',
  title: 'Viaggio Test',
  destination: 'Toscana',
  duration_days: 3,
  totalDistance: 250000, // 250 km in metri
  recommended_seasons: ['Primavera', 'Estate'],
  price: 25.00,
  slug: 'viaggio-test',
}

const mockTrip2: TripComparisonData = {
  id: 'trip-2',
  title: 'Viaggio Test 2',
  destination: 'Umbria',
  duration_days: 5,
  totalDistance: 400000, // 400 km in metri
  recommended_seasons: ['Autunno'],
  price: 35.00,
  slug: 'viaggio-test-2',
}

describe('Sistema di Confronto Viaggi', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockReturnValue(null)
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
  })

  describe('useTripComparison Hook', () => {
    it('dovrebbe inizializzare con stato vuoto', () => {
      const TestComponent = () => {
        const { selectedTrips, selectedCount, canCompare } = useTripComparison()
        return (
          <div>
            <span data-testid="count">{selectedCount}</span>
            <span data-testid="can-compare">{canCompare.toString()}</span>
            <span data-testid="trips">{selectedTrips.length}</span>
          </div>
        )
      }

      render(<TestComponent />)
      
      expect(screen.getByTestId('count')).toHaveTextContent('0')
      expect(screen.getByTestId('can-compare')).toHaveTextContent('false')
      expect(screen.getByTestId('trips')).toHaveTextContent('0')
    })

    it('dovrebbe aggiungere un viaggio correttamente', () => {
      const TestComponent = () => {
        const { addTrip, selectedCount, isSelected } = useTripComparison()
        return (
          <div>
            <button onClick={() => addTrip(mockTrip)}>Aggiungi</button>
            <span data-testid="count">{selectedCount}</span>
            <span data-testid="selected">{isSelected(mockTrip.id).toString()}</span>
          </div>
        )
      }

      render(<TestComponent />)
      
      fireEvent.click(screen.getByText('Aggiungi'))
      
      expect(screen.getByTestId('count')).toHaveTextContent('1')
      expect(screen.getByTestId('selected')).toHaveTextContent('true')
    })

    it('dovrebbe impedire di aggiungere più di 3 viaggi', () => {
      const TestComponent = () => {
        const { addTrip, selectedCount, canAddMore } = useTripComparison()
        return (
          <div>
            <button onClick={() => addTrip(mockTrip)}>Aggiungi 1</button>
            <button onClick={() => addTrip(mockTrip2)}>Aggiungi 2</button>
            <button onClick={() => addTrip({...mockTrip, id: 'trip-3'})}>Aggiungi 3</button>
            <button onClick={() => addTrip({...mockTrip, id: 'trip-4'})}>Aggiungi 4</button>
            <span data-testid="count">{selectedCount}</span>
            <span data-testid="can-add">{canAddMore.toString()}</span>
          </div>
        )
      }

      render(<TestComponent />)
      
      fireEvent.click(screen.getByText('Aggiungi 1'))
      fireEvent.click(screen.getByText('Aggiungi 2'))
      fireEvent.click(screen.getByText('Aggiungi 3'))
      fireEvent.click(screen.getByText('Aggiungi 4')) // Questo dovrebbe fallire
      
      expect(screen.getByTestId('count')).toHaveTextContent('3')
      expect(screen.getByTestId('can-add')).toHaveTextContent('false')
    })
  })

  describe('ComparisonButton', () => {
    it('dovrebbe renderizzare correttamente', () => {
      render(<ComparisonButton trip={mockTrip} />)
      
      expect(screen.getByText('Confronta')).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('dovrebbe cambiare stato quando selezionato', async () => {
      const TestComponent = () => {
        const comparison = useTripComparison()
        return (
          <div>
            <ComparisonButton trip={mockTrip} />
            <span data-testid="selected">{comparison.isSelected(mockTrip.id).toString()}</span>
          </div>
        )
      }

      render(<TestComponent />)
      
      const button = screen.getByRole('button')
      fireEvent.click(button)
      
      await waitFor(() => {
        expect(screen.getByTestId('selected')).toHaveTextContent('true')
      })
    })
  })

  describe('TripComparisonModal', () => {
    it('dovrebbe renderizzare quando aperta', () => {
      render(
        <TripComparisonModal
          isOpen={true}
          onClose={jest.fn()}
          trips={[mockTrip, mockTrip2]}
          onRemoveTrip={jest.fn()}
        />
      )
      
      expect(screen.getByText('Confronto Viaggi')).toBeInTheDocument()
      expect(screen.getByText(mockTrip.title)).toBeInTheDocument()
      expect(screen.getByText(mockTrip2.title)).toBeInTheDocument()
    })

    it('non dovrebbe renderizzare quando chiusa', () => {
      render(
        <TripComparisonModal
          isOpen={false}
          onClose={jest.fn()}
          trips={[mockTrip]}
          onRemoveTrip={jest.fn()}
        />
      )
      
      expect(screen.queryByText('Confronto Viaggi')).not.toBeInTheDocument()
    })
  })

  describe('Utility Functions', () => {
    describe('formatDistance', () => {
      it('dovrebbe formattare correttamente le distanze', () => {
        expect(formatDistance(0)).toBe('N/A')
        expect(formatDistance(500)).toBe('500 m')
        expect(formatDistance(1500)).toBe('1.5 km')
        expect(formatDistance(25000)).toBe('25 km')
        expect(formatDistance(250000)).toBe('250 km')
      })
    })

    describe('formatPrice', () => {
      it('dovrebbe formattare correttamente i prezzi', () => {
        expect(formatPrice(25)).toBe('€ 25,00')
        expect(formatPrice(25.50)).toBe('€ 25,50')
        expect(formatPrice(0)).toBe('€ 0,00')
      })
    })

    describe('convertTripToComparisonData', () => {
      it('dovrebbe convertire correttamente i dati del viaggio', () => {
        const mockFullTrip = {
          id: 'trip-1',
          title: 'Test Trip',
          destination: 'Test Destination',
          duration_days: 3,
          recommended_seasons: ['Primavera'],
          price: 25.00,
          slug: 'test-trip',
          media: [],
          stages: [],
          gpxFile: null,
        }

        const result = convertTripToComparisonData(mockFullTrip as any)
        
        expect(result.id).toBe(mockFullTrip.id)
        expect(result.title).toBe(mockFullTrip.title)
        expect(result.destination).toBe(mockFullTrip.destination)
        expect(result.duration_days).toBe(mockFullTrip.duration_days)
        expect(result.recommended_seasons).toEqual(mockFullTrip.recommended_seasons)
        expect(result.price).toBe(mockFullTrip.price)
        expect(result.slug).toBe(mockFullTrip.slug)
        expect(typeof result.totalDistance).toBe('number')
      })
    })
  })

  describe('Integrazione completa', () => {
    it('dovrebbe gestire il flusso completo di confronto', async () => {
      const TestComponent = () => {
        const comparison = useTripComparison()
        return (
          <div>
            <ComparisonButton trip={mockTrip} />
            <ComparisonButton trip={mockTrip2} />
            <TripComparisonModal
              isOpen={comparison.isModalOpen}
              onClose={comparison.closeModal}
              trips={comparison.selectedTrips}
              onRemoveTrip={comparison.removeTrip}
            />
            <button onClick={comparison.openModal}>Apri Confronto</button>
            <span data-testid="count">{comparison.selectedCount}</span>
            <span data-testid="can-compare">{comparison.canCompare.toString()}</span>
          </div>
        )
      }

      render(<TestComponent />)
      
      // Aggiungi due viaggi
      const buttons = screen.getAllByText('Confronta')
      fireEvent.click(buttons[0])
      fireEvent.click(buttons[1])
      
      await waitFor(() => {
        expect(screen.getByTestId('count')).toHaveTextContent('2')
        expect(screen.getByTestId('can-compare')).toHaveTextContent('true')
      })
      
      // Apri il confronto
      fireEvent.click(screen.getByText('Apri Confronto'))
      
      await waitFor(() => {
        expect(screen.getByText('Confronto Viaggi')).toBeInTheDocument()
      })
    })
  })
})

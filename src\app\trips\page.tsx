import { prisma } from '@/lib/core/prisma';
import { TripStatus } from '@prisma/client';
import { castToGpxFile, castToMediaItems, MediaItem, GpxFile } from '@/types/trip';
import { Prisma } from '@prisma/client';
import { TripsPageClient } from './TripsPageClient';


// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Usa i tipi generati automaticamente da Prisma per le query con include
type TripWithRelations = Prisma.TripGetPayload<{
  include: {
    user: {
      select: {
        name: true;
        email: true;
        image: true;
      };
    };
    stages: {
      select: {
        media: true;
        orderIndex: true;
      };
    };
  };
}>;

type TripWithProcessedData = TripWithRelations & {
  processedGpxFile: GpxFile | null;
  processedMedia: MediaItem[];
};



export default async function TripsPage() {
  // La pagina /trips mostra solo i viaggi pubblicati per tutti gli utenti
  // I viaggi in bozza sono visibili solo in Dashboard ("I miei Viaggi") e nel pannello Admin
  const whereClause = { status: TripStatus.Pubblicato };
  // Recupera i viaggi dal database con filtri basati sui ruoli
  const trips: TripWithRelations[] = await prisma.trip.findMany({
    
    where: whereClause,
    include: {
      user: {
        select: {
          name: true,
          email: true,
          image: true
        }
      },
      stages: {
        select: {
          media: true,
          orderIndex: true
        },
        orderBy: {
          orderIndex: 'asc'
        }
      }
    },
    orderBy: [
      {
        orderIndex: 'asc'
      },
      {
        created_at: 'desc'
      }
    ]
  });

  // Converte i GPX per ogni viaggio per uso interno, manteniamo i tipi Prisma
  const tripsWithProcessedData = trips.map(trip => ({
    ...trip,
    processedGpxFile: castToGpxFile(trip.gpxFile),
    processedMedia: castToMediaItems(trip.media || [])
  }));

  return (
    <TripsPageClient
      trips={tripsWithProcessedData}
      uniqueDestinations={uniqueDestinations}
    />
  );


}

// Metadata per SEO
export const metadata = {
  title: 'Tutti i Viaggi | RideAtlas',
  description: 'Esplora tutti gli itinerari in moto creati dalla community di RideAtlas. Trova il tuo prossimo viaggio perfetto.',
};
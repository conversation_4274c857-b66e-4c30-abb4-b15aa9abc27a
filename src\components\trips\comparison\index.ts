// src/components/trips/comparison/index.ts
/**
 * Esportazioni centralizzate per il sistema di confronto viaggi
 * Fornisce un punto di accesso unificato per tutti i componenti e hook
 */

// Componenti principali
export { ComparisonButton, CompactComparisonButton, ComparisonButtonWithBadge } from '../ComparisonButton'
export { ComparisonIndicator, CompactComparisonIndicator, MinimalComparisonIndicator } from '../ComparisonIndicator'
export { TripComparisonModal, useComparisonModal } from '../TripComparisonModal'
export { TripComparisonCard, CompactTripComparisonCard } from '../TripComparisonCard'

// Provider e context
export { 
  TripComparisonProvider, 
  useTripComparisonContext, 
  withTripComparison,
  useSimpleTripComparison 
} from '../TripComparisonProvider'

// Hook principale
export { useTripComparison } from '@/hooks/trips/useTripComparison'

// Tipi
export type {
  TripComparisonData,
  ComparisonState,
  ComparisonHook,
  ComparisonButtonProps,
  TripComparisonModalProps,
  ComparisonIndicatorProps,
  TripComparisonCardProps
} from '@/types/comparison'

// Utility
export {
  convertTripToComparisonData,
  convertTripsToComparisonData,
  formatDistance,
  formatPrice,
  formatDuration,
  sortTripsForComparison,
  validateTripForComparison
} from '@/lib/trips/comparison-utils'

// Costanti
export { COMPARISON_CONFIG, COMPARISON_MESSAGES } from '@/types/comparison'

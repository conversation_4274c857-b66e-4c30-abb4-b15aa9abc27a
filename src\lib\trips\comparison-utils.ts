// src/lib/trips/comparison-utils.ts
/**
 * Utility per la conversione e gestione dei dati per il confronto viaggi
 */

import { TripComparisonData } from '@/types/comparison'
import { Trip, MediaItem, castToMediaItems } from '@/types/trip'
import { calculateTotalDistance } from './trip-utils'

/**
 * Converte un viaggio completo in dati per il confronto
 * @param trip Viaggio completo dal database
 * @returns Dati ottimizzati per il confronto
 */
export function convertTripToComparisonData(trip: Trip): TripComparisonData {
  // Calcola la distanza totale usando la funzione esistente
  const totalDistance = calculateTotalDistance(trip)
  
  // Estrae l'immagine di copertina
  const mediaItems = castToMediaItems(trip.media)
  const coverImage = getCoverImage(mediaItems)
  
  return {
    id: trip.id,
    title: trip.title,
    destination: trip.destination,
    duration_days: trip.duration_days,
    totalDistance,
    recommended_seasons: trip.recommended_seasons,
    price: Number(trip.price), // Converte Decimal in number
    coverImage,
    slug: trip.slug,
  }
}

/**
 * Estrae l'immagine di copertina da una lista di media
 * @param mediaItems Lista di media items
 * @returns Prima immagine trovata o undefined
 */
function getCoverImage(mediaItems: MediaItem[]): MediaItem | undefined {
  // Cerca prima le immagini
  const image = mediaItems.find(item => item.type === 'image')
  if (image) return image
  
  // Se non ci sono immagini, cerca video con thumbnail
  const videoWithThumbnail = mediaItems.find(
    item => item.type === 'video' && item.thumbnailUrl
  )
  if (videoWithThumbnail) return videoWithThumbnail
  
  return undefined
}

/**
 * Formatta la distanza per la visualizzazione
 * @param distanceInMeters Distanza in metri
 * @returns Stringa formattata (es. "125.5 km")
 */
export function formatDistance(distanceInMeters: number): string {
  if (distanceInMeters === 0) return 'N/A'
  
  const km = distanceInMeters / 1000
  
  if (km < 1) {
    return `${Math.round(distanceInMeters)} m`
  }
  
  if (km < 10) {
    return `${km.toFixed(1)} km`
  }
  
  return `${Math.round(km)} km`
}

/**
 * Formatta il prezzo per la visualizzazione
 * @param price Prezzo in euro
 * @returns Stringa formattata (es. "€ 25,00")
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR',
  }).format(price)
}

/**
 * Formatta la durata per la visualizzazione
 * @param days Numero di giorni
 * @returns Stringa formattata (es. "3 giorni")
 */
export function formatDuration(days: number): string {
  if (days === 1) return '1 giorno'
  return `${days} giorni`
}

/**
 * Converte una lista di viaggi in dati per il confronto
 * @param trips Lista di viaggi completi
 * @returns Lista di dati ottimizzati per il confronto
 */
export function convertTripsToComparisonData(trips: Trip[]): TripComparisonData[] {
  return trips.map(convertTripToComparisonData)
}

/**
 * Ordina i viaggi per confronto secondo criteri predefiniti
 * @param trips Lista di viaggi da ordinare
 * @param sortBy Criterio di ordinamento
 * @returns Lista ordinata
 */
export function sortTripsForComparison(
  trips: TripComparisonData[], 
  sortBy: 'price' | 'duration' | 'distance' | 'title' = 'title'
): TripComparisonData[] {
  return [...trips].sort((a, b) => {
    switch (sortBy) {
      case 'price':
        return a.price - b.price
      case 'duration':
        return a.duration_days - b.duration_days
      case 'distance':
        return a.totalDistance - b.totalDistance
      case 'title':
      default:
        return a.title.localeCompare(b.title, 'it-IT')
    }
  })
}

/**
 * Valida se un viaggio può essere aggiunto al confronto
 * @param trip Viaggio da validare
 * @returns Oggetto con risultato e messaggio di errore
 */
export function validateTripForComparison(trip: TripComparisonData): {
  isValid: boolean
  error?: string
} {
  if (!trip.id || !trip.title) {
    return {
      isValid: false,
      error: 'Dati del viaggio incompleti'
    }
  }
  
  if (!trip.destination) {
    return {
      isValid: false,
      error: 'Destinazione del viaggio mancante'
    }
  }
  
  if (trip.duration_days <= 0) {
    return {
      isValid: false,
      error: 'Durata del viaggio non valida'
    }
  }
  
  return { isValid: true }
}

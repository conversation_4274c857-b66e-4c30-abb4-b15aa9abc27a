// src/components/trips/TripComparisonProvider.tsx
/**
 * Provider che integra tutto il sistema di confronto viaggi
 * Fornisce il contesto globale e i componenti necessari
 */

'use client'

import { createContext, useContext, ReactNode } from 'react'
import { useTripComparison } from '@/hooks/trips/useTripComparison'
import { ComparisonHook } from '@/types/comparison'
import { TripComparisonModal } from './TripComparisonModal'
import { ComparisonIndicator } from './ComparisonIndicator'

// Context per condividere lo stato del confronto
const TripComparisonContext = createContext<ComparisonHook | null>(null)

interface TripComparisonProviderProps {
  children: ReactNode
  showIndicator?: boolean
  showModal?: boolean
}

/**
 * Provider principale per il sistema di confronto viaggi
 */
export function TripComparisonProvider({ 
  children, 
  showIndicator = true,
  showModal = true 
}: TripComparisonProviderProps) {
  const comparisonHook = useTripComparison()
  
  return (
    <TripComparisonContext.Provider value={comparisonHook}>
      {children}
      
      {/* Indicatore floating */}
      {showIndicator && (
        <ComparisonIndicator
          selectedCount={comparisonHook.selectedCount}
          onOpenComparison={comparisonHook.openModal}
          onClearAll={comparisonHook.clearAll}
        />
      )}
      
      {/* Modale di confronto */}
      {showModal && (
        <TripComparisonModal
          isOpen={comparisonHook.isModalOpen}
          onClose={comparisonHook.closeModal}
          trips={comparisonHook.selectedTrips}
          onRemoveTrip={comparisonHook.removeTrip}
        />
      )}
    </TripComparisonContext.Provider>
  )
}

/**
 * Hook per accedere al contesto del confronto viaggi
 */
export function useTripComparisonContext(): ComparisonHook {
  const context = useContext(TripComparisonContext)
  
  if (!context) {
    throw new Error(
      'useTripComparisonContext deve essere usato all\'interno di un TripComparisonProvider'
    )
  }
  
  return context
}

/**
 * HOC per wrappare componenti che necessitano del confronto viaggi
 */
export function withTripComparison<P extends object>(
  Component: React.ComponentType<P>
) {
  return function WrappedComponent(props: P) {
    return (
      <TripComparisonProvider>
        <Component {...props} />
      </TripComparisonProvider>
    )
  }
}

/**
 * Hook semplificato per componenti che non necessitano del provider completo
 * Utile per componenti che vogliono solo accedere alle funzioni di confronto
 */
export function useSimpleTripComparison() {
  try {
    return useTripComparisonContext()
  } catch {
    // Fallback se non c'è un provider
    return useTripComparison()
  }
}

// src/components/trips/ComparisonIndicator.tsx
/**
 * Indicatore floating che mostra il numero di viaggi selezionati
 * e permette di aprire il confronto o cancellare la selezione
 */

'use client'

import { Scale, X, Eye } from 'lucide-react'
import { ComparisonIndicatorProps } from '@/types/comparison'

export function ComparisonIndicator({
  selectedCount,
  onOpenComparison,
  onClearAll,
  className = ''
}: ComparisonIndicatorProps) {
  // Non mostrare l'indicatore se non ci sono viaggi selezionati
  if (selectedCount === 0) return null
  
  const canCompare = selectedCount >= 2
  
  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-[280px]">
        {/* Header con conteggio */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Scale className="w-5 h-5 text-blue-600" />
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                {selectedCount}
              </div>
            </div>
            <span className="font-medium text-gray-900">
              {selectedCount === 1 ? '1 viaggio selezionato' : `${selectedCount} viaggi selezionati`}
            </span>
          </div>
          
          <button
            onClick={onClearAll}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            title="Cancella selezione"
            aria-label="Cancella tutti i viaggi selezionati"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {/* Messaggio di stato */}
        <div className="mb-3">
          {selectedCount === 1 && (
            <p className="text-sm text-gray-600">
              Seleziona almeno un altro viaggio per confrontarli
            </p>
          )}
          {selectedCount === 2 && (
            <p className="text-sm text-green-600">
              Perfetto! Ora puoi confrontare i viaggi
            </p>
          )}
          {selectedCount === 3 && (
            <p className="text-sm text-blue-600">
              Limite massimo raggiunto (3 viaggi)
            </p>
          )}
        </div>
        
        {/* Pulsanti azione */}
        <div className="flex gap-2">
          <button
            onClick={onOpenComparison}
            disabled={!canCompare}
            className={`
              flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium text-sm transition-all
              ${canCompare
                ? 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }
            `}
            aria-label="Apri confronto viaggi"
          >
            <Eye className="w-4 h-4" />
            Confronta
          </button>
          
          <button
            onClick={onClearAll}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium"
            aria-label="Cancella tutti i viaggi selezionati"
          >
            Cancella
          </button>
        </div>
      </div>
    </div>
  )
}

/**
 * Versione compatta dell'indicatore per spazi ridotti
 */
export function CompactComparisonIndicator({
  selectedCount,
  onOpenComparison,
  onClearAll,
  className = ''
}: ComparisonIndicatorProps) {
  if (selectedCount === 0) return null
  
  const canCompare = selectedCount >= 2
  
  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      <div className="bg-blue-600 text-white rounded-full shadow-lg p-3 flex items-center gap-3">
        {/* Badge con conteggio */}
        <div className="relative">
          <Scale className="w-5 h-5" />
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-white text-blue-600 text-xs rounded-full flex items-center justify-center font-bold">
            {selectedCount}
          </div>
        </div>
        
        {/* Pulsanti azione compatti */}
        <div className="flex gap-2">
          {canCompare && (
            <button
              onClick={onOpenComparison}
              className="bg-white text-blue-600 px-3 py-1 rounded-full text-sm font-medium hover:bg-gray-100 transition-colors"
              aria-label="Confronta viaggi"
            >
              Confronta
            </button>
          )}
          
          <button
            onClick={onClearAll}
            className="text-white hover:text-gray-200 transition-colors p-1"
            title="Cancella selezione"
            aria-label="Cancella selezione"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

/**
 * Indicatore minimale che mostra solo il conteggio
 * Utile per header o barre di navigazione
 */
export function MinimalComparisonIndicator({
  selectedCount,
  onOpenComparison,
  className = ''
}: Pick<ComparisonIndicatorProps, 'selectedCount' | 'onOpenComparison' | 'className'>) {
  if (selectedCount === 0) return null
  
  const canCompare = selectedCount >= 2
  
  return (
    <button
      onClick={onOpenComparison}
      disabled={!canCompare}
      className={`
        relative inline-flex items-center gap-2 px-3 py-2 rounded-lg transition-all
        ${canCompare
          ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
          : 'bg-gray-100 text-gray-500 cursor-not-allowed'
        }
        ${className}
      `}
      aria-label={`${selectedCount} viaggi selezionati per il confronto`}
    >
      <Scale className="w-4 h-4" />
      <span className="text-sm font-medium">{selectedCount}</span>
      
      {/* Badge di notifica */}
      <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-600 rounded-full">
        <span className="sr-only">{selectedCount} viaggi selezionati</span>
      </div>
    </button>
  )
}

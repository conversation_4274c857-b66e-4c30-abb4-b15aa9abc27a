// src/hooks/trips/useTripComparison.ts
/**
 * Hook personalizzato per gestire lo stato del confronto viaggi
 * Gestisce selezione, aggiunta, rimozione e persistenza dello stato
 */

'use client'

import { useState, useCallback, useEffect } from 'react'
import { useToast } from '@/hooks/ui/useToast'
import { 
  TripComparisonData, 
  ComparisonState, 
  ComparisonHook,
  COMPARISON_CONFIG,
  COMPARISON_MESSAGES 
} from '@/types/comparison'

/**
 * Stato iniziale del confronto
 */
const INITIAL_STATE: ComparisonState = {
  selectedTrips: [],
  isModalOpen: false,
  maxTrips: COMPARISON_CONFIG.MAX_TRIPS,
}

/**
 * Utility per la gestione del localStorage
 */
const storage = {
  save: (trips: TripComparisonData[]) => {
    try {
      const data = {
        trips,
        timestamp: Date.now(),
      }
      localStorage.setItem(COMPARISON_CONFIG.STORAGE_KEY, JSON.stringify(data))
    } catch (error) {
      console.warn('Impossibile salvare i dati del confronto:', error)
    }
  },
  
  load: (): TripComparisonData[] => {
    try {
      const stored = localStorage.getItem(COMPARISON_CONFIG.STORAGE_KEY)
      if (!stored) return []
      
      const data = JSON.parse(stored)
      const isExpired = Date.now() - data.timestamp > COMPARISON_CONFIG.SESSION_TIMEOUT
      
      if (isExpired) {
        storage.clear()
        return []
      }
      
      return data.trips || []
    } catch (error) {
      console.warn('Impossibile caricare i dati del confronto:', error)
      return []
    }
  },
  
  clear: () => {
    try {
      localStorage.removeItem(COMPARISON_CONFIG.STORAGE_KEY)
    } catch (error) {
      console.warn('Impossibile cancellare i dati del confronto:', error)
    }
  },
}

/**
 * Hook principale per la gestione del confronto viaggi
 */
export function useTripComparison(): ComparisonHook {
  const [state, setState] = useState<ComparisonState>(INITIAL_STATE)
  const { showSuccess, showError } = useToast()

  // Carica i dati salvati al mount
  useEffect(() => {
    const savedTrips = storage.load()
    if (savedTrips.length > 0) {
      setState(prev => ({
        ...prev,
        selectedTrips: savedTrips,
      }))
    }
  }, [])

  // Salva automaticamente quando cambia la selezione
  useEffect(() => {
    if (state.selectedTrips.length > 0) {
      storage.save(state.selectedTrips)
    } else {
      storage.clear()
    }
  }, [state.selectedTrips])

  /**
   * Aggiunge un viaggio al confronto
   * @param trip Viaggio da aggiungere
   * @returns true se aggiunto con successo, false altrimenti
   */
  const addTrip = useCallback((trip: TripComparisonData): boolean => {
    // Verifica se il viaggio è già selezionato
    const isAlreadySelected = state.selectedTrips.some(t => t.id === trip.id)
    if (isAlreadySelected) {
      showError(COMPARISON_MESSAGES.TRIP_ALREADY_SELECTED)
      return false
    }

    // Verifica il limite massimo
    if (state.selectedTrips.length >= COMPARISON_CONFIG.MAX_TRIPS) {
      showError(COMPARISON_MESSAGES.MAX_TRIPS_REACHED)
      return false
    }

    setState(prev => ({
      ...prev,
      selectedTrips: [...prev.selectedTrips, trip],
    }))

    showSuccess(COMPARISON_MESSAGES.TRIP_ADDED)
    return true
  }, [state.selectedTrips, showError, showSuccess])

  /**
   * Rimuove un viaggio dal confronto
   * @param tripId ID del viaggio da rimuovere
   */
  const removeTrip = useCallback((tripId: string) => {
    setState(prev => ({
      ...prev,
      selectedTrips: prev.selectedTrips.filter(trip => trip.id !== tripId),
    }))
    showSuccess(COMPARISON_MESSAGES.TRIP_REMOVED)
  }, [showSuccess])

  /**
   * Rimuove tutti i viaggi dal confronto
   */
  const clearAll = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedTrips: [],
      isModalOpen: false,
    }))
    storage.clear()
    showSuccess(COMPARISON_MESSAGES.ALL_CLEARED)
  }, [showSuccess])

  /**
   * Apre la modale di confronto
   */
  const openModal = useCallback(() => {
    if (state.selectedTrips.length < COMPARISON_CONFIG.MIN_TRIPS_FOR_COMPARISON) {
      showError(COMPARISON_MESSAGES.MIN_TRIPS_REQUIRED)
      return
    }
    
    setState(prev => ({
      ...prev,
      isModalOpen: true,
    }))
  }, [state.selectedTrips.length, showError])

  /**
   * Chiude la modale di confronto
   */
  const closeModal = useCallback(() => {
    setState(prev => ({
      ...prev,
      isModalOpen: false,
    }))
  }, [])

  /**
   * Verifica se un viaggio è selezionato
   * @param tripId ID del viaggio da verificare
   * @returns true se il viaggio è selezionato
   */
  const isSelected = useCallback((tripId: string): boolean => {
    return state.selectedTrips.some(trip => trip.id === tripId)
  }, [state.selectedTrips])

  // Valori derivati
  const canAddMore = state.selectedTrips.length < COMPARISON_CONFIG.MAX_TRIPS
  const selectedCount = state.selectedTrips.length
  const canCompare = selectedCount >= COMPARISON_CONFIG.MIN_TRIPS_FOR_COMPARISON

  return {
    // Stato
    selectedTrips: state.selectedTrips,
    isModalOpen: state.isModalOpen,
    canAddMore,
    selectedCount,
    
    // Azioni
    addTrip,
    removeTrip,
    clearAll,
    openModal,
    closeModal,
    isSelected,
    
    // Utilità
    canCompare,
  }
}
